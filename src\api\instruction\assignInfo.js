import request from '@/utils/request'

// 查询交办单信息列表
export function listAssignInfo(query) {
  return request({
    url: '/instruction/assignInfo/list',
    method: 'get',
    params: query,
  })
}

// 查询交办单信息详细
export function getAssignInfo(id) {
  return request({
    url: '/instruction/assignInfo/' + id,
    method: 'get',
  })
}

// 新增交办单信息
export function addAssignInfo(data) {
  return request({
    url: '/instruction/assignInfo',
    method: 'post',
    data: data,
  })
}

// 修改交办单信息
export function updateAssignInfo(data) {
  return request({
    url: '/instruction/assignInfo',
    method: 'put',
    data: data,
  })
}

// 删除交办单信息
export function delAssignInfo(id) {
  return request({
    url: '/instruction/assignInfo/' + id,
    method: 'delete',
  })
}

// 导出交办单信息列表
export function exportAssignInfo() {
  return request({
    url: '/instruction/assignInfo/export',
    method: 'post',
    responseType: 'blob',
  })
}
