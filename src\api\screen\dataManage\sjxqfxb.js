import request from '@/utils/request'

// 查询数据需求分析-城市大脑ODPS指标列表
export function listSjxqfxb(query) {
  return request({
    url: '/sjxqfx/sjxqfxb/list',
    method: 'get',
    params: query
  })
}

// 查询数据需求分析-城市大脑ODPS指标详细
export function getSjxqfxb(id) {
  return request({
    url: '/sjxqfx/sjxqfxb/' + id,
    method: 'get'
  })
}

// 新增数据需求分析-城市大脑ODPS指标
export function addSjxqfxb(data) {
  return request({
    url: '/sjxqfx/sjxqfxb',
    method: 'post',
    data: data
  })
}

// 修改数据需求分析-城市大脑ODPS指标
export function updateSjxqfxb(data) {
  return request({
    url: '/sjxqfx/sjxqfxb',
    method: 'put',
    data: data
  })
}

// 删除数据需求分析-城市大脑ODPS指标
export function delSjxqfxb(id) {
  return request({
    url: '/sjxqfx/sjxqfxb/' + id,
    method: 'delete'
  })
}

// 导出数据需求分析-城市大脑ODPS指标
export function exportSjxqfxb(query) {
  return request({
    url: '/sjxqfx/sjxqfxb/export',
    method: 'get',
    params: query
  })
}

// 获取指标id列表
export function getidList() {
  return request({
    url: '/sjxqfx/sjxqfxb/getIndicator',
    method: 'get'
  })
}

// 获取质量反馈表列表
export function getTableList(id) {
  return request({
    url: `/sjxqfx/sjxqfxb/zlfkwt/${id}`,
    method: 'get'
  })
}

// 质量反馈
export function tofeedback(data) {
  return request({
    url: `/sjxqfx/sjxqfxb/sfmzkfxq`,
    method: 'post',
    data,
    dataType: 'json'
  })
}

// 根据指标id获取填充内容
export function getIdInfo(id) {
  return request({
    url: `/sjxqfx/sjxqfxb/getIndicatorById/${id}`,
    method: 'get'
  })
}

//获取英文表名列表
export function getETableList(params) {
  return request({
    url: `/sjzyqd/sjzyqd/zyqd/getYwmcList`,
    method: 'get',
    params
  })
}

//根据英文表名获取填充内容
export function getETableinfo(params) {
  return request({
    url: `/sjzyqd/sjzyqd/zyqd/getSjByYwmc`,
    method: 'get',
    params
  })
}

//总师确认需求
export function zsqrxq(data) {
  return request({
    url: `/sjxqfx/sjxqfxb/zsqrxq`,
    method: 'post',
    data
  })
}

//开发人员修改确认
export function kfryxg(data) {
  return request({
    url: '/sjxqfx/sjxqfxb/kfryxg',
    method: 'post',
    data: data
  })
}
