import request from '@/utils/request'

// 获取指标列表
export function getBindRoleList(query) {
  return request({
    url: '/screen/zbrole/getBindRoleList',
    method: 'post',
    data: query
  })
}

// 添加指标调用权限
export function addZbRole(query) {
  return request({
    url: '/screen/zbrole/addZbRole',
    method: 'post',
    data: query
  })
}

// 批量添加指标调用权限
export function batchAddzbroles(query) {
  return request({
    url: '/screen/zbrole/batchAddzbroles',
    method: 'post',
    data: query
  })
}
// 删除指标调用权限
export function removeZbRole(query) {
  return request({
    url: '/screen/zbrole/removeZbRole',
    method: 'post',
    data: query
  })
}
