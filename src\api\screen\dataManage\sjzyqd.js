import request from '@/utils/request'

// 查询城市大脑数据资源清单列表
export function listSjzyqd(query) {
  return request({
    url: '/sjzyqd/sjzyqd/list',
    method: 'get',
    params: query
  })
}

// 查询城市大脑数据资源清单详细
export function getSjzyqd(id) {
  return request({
    url: '/sjzyqd/sjzyqd/' + id,
    method: 'get'
  })
}

// 新增城市大脑数据资源清单
export function addSjzyqd(data) {
  return request({
    url: '/sjzyqd/sjzyqd',
    method: 'post',
    data: data
  })
}

// 修改城市大脑数据资源清单
export function updateSjzyqd(data) {
  return request({
    url: '/sjzyqd/sjzyqd',
    method: 'put',
    data: data
  })
}

// 删除城市大脑数据资源清单
export function delSjzyqd(id) {
  return request({
    url: '/sjzyqd/sjzyqd/' + id,
    method: 'delete'
  })
}

// 导出城市大脑数据资源清单
export function exportSjzyqd(query) {
  return request({
    url: '/sjzyqd/sjzyqd/export',
    method: 'get',
    params: query
  })
}

// 查询项目下拉列表
export function getXmList() {
  return request({
    url: '/sjzyqd/sjzyqd/zyqd/getXmList',
    method: 'get'
  })
}

// 查询数据使用情况列表
export function getSyqkList() {
  return request({
    url: '/sjzyqd/sjzyqd/zyqd/getSjsyqkList',
    method: 'get'
  })
}

//数据清单 申请审批
export function sqjg(data) {
  return request({
    url: '/sjzyqd/sjzyqd//zyqd/sqjg',
    method: 'post',
    data: data
  })
}
// 模板下载
export function getTemp() {
  return request({
    url: '/sjzyqd/sjzyqd/zyqd/importTemplate',
    method: 'get'
  })
}
