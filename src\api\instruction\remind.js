import request from '@/utils/request'

// 查询提醒单-信息列表
export function listRemind(query) {
  return request({
    url: '/instruction/remind/list',
    method: 'get',
    params: query,
  })
}

// 查询提醒单-信息详细
export function getRemind(id) {
  return request({
    url: '/instruction/remind/' + id,
    method: 'get',
  })
}

// 新增提醒单-信息
export function addRemind(data) {
  return request({
    url: '/instruction/remind',
    method: 'post',
    data: data,
  })
}

// 修改提醒单-信息
export function updateRemind(data) {
  return request({
    url: '/instruction/remind',
    method: 'put',
    data: data,
  })
}

// 删除提醒单-信息
export function delRemind(id) {
  return request({
    url: '/instruction/remind/' + id,
    method: 'delete',
  })
}
