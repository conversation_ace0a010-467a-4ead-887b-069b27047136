import request from '@/utils/request'

// 一键排班
export function yijianYipb(data) {
  return request({
    url: '/xzzfj/dutyInformation/yipb',
    method: 'post',
    data: data
  })
}

// 查询值班人员列表
export function listDutyPersonnel(query) {
  return request({
    url: '/xzzfj/dutyPersonnel/list',
    method: 'get',
    params: query
  })
}

// 查询值班人员详细
export function getDutyPersonnel(id) {
  return request({
    url: '/xzzfj/dutyPersonnel/' + id,
    method: 'get'
  })
}

// 新增值班人员
export function addDutyPersonnel(data) {
  return request({
    url: '/xzzfj/dutyPersonnel',
    method: 'post',
    data: data
  })
}

// 修改值班人员
export function updateDutyPersonnel(data) {
  return request({
    url: '/xzzfj/dutyPersonnel',
    method: 'put',
    data: data
  })
}

// 删除值班人员
export function delDutyPersonnel(id) {
  return request({
    url: '/xzzfj/dutyPersonnel/remove/' + id,
    method: 'put'
  })
}

// 获取用户区域
export function getAreaName() {
  return request({
    url: '/xzzfj/xzzfjZfsb/getArea',
    method: 'get',
  })
}

// 获取用户区域对应的乡镇街道列表
export function getTownList(query) {
  return request({
    url: '/xzzfj/dutyPersonnel/getTown',
    method: 'get',
    params: query
  })
}

// 部门下拉数据
export function getDepartment(query) {
  return request({
    url: '/xzzfj/dutyPersonnel/getDeptList',
    method: 'get',
    params: query
  })
}

// 导出
export function exportData(params) {
  return request({
    url: '/xzzfj/dutyPersonnel/export',
    method: 'post',
    responseType: 'blob',
    params
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/xzzfj/dutyPersonnel/exportMb',
    method: 'post',
  })
}

// 轨迹
export function trace(query) {
  return request({
    url: '/xzzfj/lwzfjly/trace',
    method: 'get',
    params: query
  })
}
