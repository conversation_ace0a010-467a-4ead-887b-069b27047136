import request from '@/utils/request'

// 查询指令基本信息列表
// export function listInfo(query) {
//   return request({
//     url: '/instruction/info/list',
//     method: 'get',
//     params: query,
//   })
// }
export function listInfo(query) {
  return request({
    // url: '/instruction/info/testInstructionList',
    url: '/instruction/info/testInstructionListNew',
    method: 'get',
    params: query,
  })
}

// 查询指令基本信息详细
export function getInfo(id) {
  return request({
    url: '/instruction/info/' + id,
    method: 'get',
  })
}

// 新增指令基本信息
export function addInfo(data) {
  return request({
    url: '/instruction/info',
    method: 'post',
    data: data,
  })
}

// 修改指令基本信息
export function updateInfo(data) {
  return request({
    url: '/instruction/info',
    method: 'put',
    data: data,
  })
}

// 删除指令基本信息
export function delInfo(id) {
  return request({
    url: '/instruction/info/remove/' + id,
    method: 'put',
  })
}

// 根据指令id获取指令流程相关信息
export function getInfoStream(id, type) {
  return request({
    // url: '/instruction/info/getProcessById/' + id,
    url: '/instruction/info/testGetProcessById/' + id + '/' + type,
    method: 'get',
    timeout: 10 * 1000 * 6,
  })
}

// 新增指令基本信息
export function submitInfo(data) {
  return request({
    // url: '/instruction/info/submit',
    url: '/instruction/info/testSubmit',
    method: 'post',
    data: data,
  })
}

// 查询用户同级部门及以下部门
export function getDept(params) {
  return request({
    url: '/system/user/getDept',
    method: 'get',
    params: params,
  })
}

// 待办红点统计
export function getStatistics(params) {
  return request({
    url: '/instruction/info/testRedStatistics',
    method: 'get',
    params: params,
  })
}

// 待办红点统计
export function editIsRelease(data) {
  return request({
    url: '/instruction/info/editIsRelease',
    method: 'put',
    data: data,
  })
}

/**
 * 导出指令列表
 */
export function exportInfo(data) {
  return request({
    url: '/instruction/info/export',
    method: 'post',
    data: data,
    responseType: 'blob',
  })
}

// 删除指令基本信息
export function deleteInfo(params) {
  return request({
    url: '/instruction/info/batchRemove',
    method: 'put',
    params: params,
  })
}
