import request from '@/utils/request'

// 查询事件基本信息列表
export function listEvent(query) {
  return request({
    url: '/jazzInstruction/event/list',
    method: 'get',
    params: query,
  })
}

// 查询事件基本信息详细
export function getEvent(id) {
  return request({
    url: '/jazzInstruction/event/' + id,
    method: 'get',
  })
}

// 新增事件基本信息
export function addEvent(data) {
  return request({
    url: '/jazzInstruction/event',
    method: 'post',
    data: data,
  })
}

// 修改事件基本信息
export function updateEvent(data) {
  return request({
    url: '/jazzInstruction/event',
    method: 'put',
    data: data,
  })
}

// 删除事件基本信息
export function delEvent(id) {
  return request({
    url: '/jazzInstruction/event/' + id,
    method: 'delete',
  })
}

// 查询群体基本信息列表
// export function listGroup(query) {
//   return request({
//     url: '/instruction/group/list',
//     method: 'get',
//     params: query,
//   })
// }

// 查询风险排查列表
export function listItem(query) {
  return request({
    url: '/item/info/list',
    method: 'get',
    params: query,
  })
}

// 通过事件id生成指令
export function addInstruction(id) {
  return request({
    url: '/jazzInstruction/event/addInstruction/' + id,
    method: 'get',
  })
}

// 根据群体id查询相关联事件
export function getEventListByGroupId(id) {
  return request({
    url: '/jazzInstruction/event/getEventListByGroupId/' + id,
    method: 'get',
  })
}

// 导入事件模板下载接口
export function importEventTemplate(query) {
  return request({
    url: 'jazzInstruction/event/importTemplate',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 导出事件数据接口
export function exportEvents(query) {
  return request({
    url: 'jazzInstruction/event/export',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 导入事件数据接口
export function uploadEvent(query) {
  return request({
    url: 'jazzInstruction/event/importData',
    method: 'post',
  })
}
