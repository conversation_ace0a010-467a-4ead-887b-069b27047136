import request from '@/utils/request'

// 查询数据反馈情况-委办局对接人列表
export function listPerson(query) {
  return request({
    url: '/sjxqperson/person/list',
    method: 'get',
    params: query
  })
}

// 查询数据反馈情况-委办局对接人详细
export function getPerson(id) {
  return request({
    url: '/sjxqperson/person/' + id,
    method: 'get'
  })
}

// 新增数据反馈情况-委办局对接人
export function addPerson(data) {
  return request({
    url: '/sjxqperson/person',
    method: 'post',
    data: data
  })
}

// 修改数据反馈情况-委办局对接人
export function updatePerson(data) {
  return request({
    url: '/sjxqperson/person',
    method: 'put',
    data: data
  })
}

// 删除数据反馈情况-委办局对接人
export function delPerson(id) {
  return request({
    url: '/sjxqperson/person/' + id,
    method: 'delete'
  })
}
