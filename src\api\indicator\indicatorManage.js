import request from '@/utils/request'

// 新增指标管理分类
export function addCategory (data) {
  return request({
    url: '/system/indicatorBackCategory',
    method: 'post',
    data
  })
}

// 查询指标管理分类列表
export function getCategoryList (params) {
  return request({
    url: '/system/indicatorBackCategory/list',
    method: 'get',
    params
  })
}

// getTree
export function getCategoryTree (params) {
  return request({
    url: '/system/indicatorBackCategory/tree',
    method: 'get',
    params
  })
}

// 获取指标管理分类详细信息
export function getCategoryDetail (id) {
  return request({
    url: `/system/indicatorBackCategory/${id}`,
    method: 'get'
  })
}

// 修改指标管理分类
export function editCategory (data) {
  return request({
    url: '/system/indicatorBackCategory',
    method: 'put',
    data
  })
}

// 删除指标管理分类
export function delCategory (id) {
  return request({
    url: `/system/indicatorBackCategory/${id}`,
    method: 'delete'
  })
}

// 查询标签列表
export function getLabeList (params) {
  return request({
    url: '/screen/indicatorBackLabel/list',
    method: 'get',
    params
  })
}

// 获得最大的排序数
export function getMaxSort () {
  return request({
    url: '/screen/indicatorBackDetail/getMaxSort',
    method: 'get'
  })
}

// 查询指标详情列表
export function getZblist (params) {
  return request({
    url: '/screen/indicatorBackDetail/list',
    method: 'get',
    params
  })
}

// 获取责任部门
export function getDeptData () {
  return request({
    // url: '/system/sjqkhz/getDeptData',
    url: '/screen/indicatorBackDetail/getDeptData',
    method: 'get'
  })
}

// 搜索维度字典名称
export function findTitleManage (data) {
  return request({
    url: '/system/dwdTableManage/findTitleManage',
    method: 'post',
    data
  })
}

// 新增原子指标
export function addYzzb (data) {
  return request({
    url: '/screen/indicatorBackDetail',
    method: 'post',
    data
  })
}

// 获取指标详情详细信息
export function getIndicatorDetail (id) {
  return request({
    url: `/screen/indicatorBackDetail/${id}`,
    method: 'get'
  })
}

// 修改指标管理分类
export function editIndicatorDetail (data) {
  return request({
    url: '/screen/indicatorBackDetail',
    method: 'put',
    data
  })
}

// 删除指标详情
export function delIndicatorDetail (id) {
  return request({
    url: `/screen/indicatorBackDetail/${id}`,
    method: 'delete'
  })
}


// 查询权限规则列表
export function getRuleList (params) {
  return request({
    url: `/screen/indicatorBackWeightRule/list`,
    method: 'get',
    params
  })
}

// 审批
export function approval (data) {
  return request({
    url: '/screen/indicatorBackDetail/approval',
    method: 'post',
    data
  })
}

// 批量修改
export function batchUpdate (data) {
  return request({
    url: '/screen/indicatorBackDetail/batchUpdate',
    method: 'post',
    data
  })
}

// 查询异常指标列表
export function pageException (params) {
  return request({
    url: '/screen/indicatorBackDetail/pageException',
    method: 'get',
    params
  })
}

// 查询异常指标详情
export function getExceptionDetail (id) {
  return request({
    url: `/screen/indicatorBackDetail/getException/${id}`,
    method: 'get'
  })
}

// 查询修改记录
export function getListUpdateLog (params) {
  return request({
    url: '/screen/indicatorBackDetail/listUpdateLog',
    method: 'get',
    params
  })
}

// 统计分析上半部分
export function getIndicatorBoardTop() {
  return request({
    url: '/screen/indicatorBoard/top',
    method: 'get'
  })
}

// 统计分析下半部分
export function getIndicatorBoardBottom(params) {
  return request({
    url: '/screen/indicatorBoard/bottom',
    method: 'get',
    params
  })
}

// 查询指标值
export function getIndicatorValue (data) {
  return request({
    url: '/screen/indicatorBackDetail/getIndicatorValue',
    method: 'post',
    data
  })
}

// 查询规则管理
export function getRuleManage (id) {
  return request({
    url: `/screen/indicatorBackDetail/getRuleManage/${id}`,
    method: 'get'
  })
}

// 保存规则管理
export function saveRuleManage (data) {
  return request({
    url: '/screen/indicatorBackDetail/saveRuleManage',
    method: 'post',
    data
  })
}

