import request from '@/utils/request'

// 查询分析研判列表
export function listJudement(query) {
  return request({
    url: '/instruction/judement/list',
    method: 'get',
    params: query,
  })
}

// 查询分析研判详细
export function getJudement(id) {
  return request({
    url: '/instruction/judement/' + id,
    method: 'get',
  })
}

// 新增分析研判
export function addJudement(data) {
  return request({
    url: '/instruction/judement',
    method: 'post',
    data: data,
  })
}

// 修改分析研判
export function updateJudement(data) {
  return request({
    url: '/instruction/judement',
    method: 'put',
    data: data,
  })
}

// 删除分析研判
export function delJudement(id) {
  return request({
    url: '/instruction/judement/' + id,
    method: 'delete',
  })
}

// 获取分析研判数、待研判数、已研判数
export function getJudementData(id) {
  return request({
    url: '/instruction/judement/getData',
    method: 'get',
  })
}
