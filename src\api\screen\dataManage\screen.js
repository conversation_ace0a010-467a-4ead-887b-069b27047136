import request from '@/utils/request'

// 使用板块-各模块数据使用情况
export function getOnlineNum(params) {
  return request({
    url: '/BigScreen/getOnline',
    method: 'get',
    params: params,
  })
}

// 使用模块-最多使用次数的5张表
export function getTopFive(params) {
  return request({
    url: '/BigScreen/getTopFive',
    method: 'get',
    params: params,
  })
}

// 质量板块
export function qualityInfos(params) {
  return request({
    url: '/BigScreen/qualityInfos',
    method: 'get',
    params: params,
  })
}

// 开发板块-柱状图数据
export function getPriority(params) {
  return request({
    url: '/BigScreen/Priority',
    method: 'get',
    params: params,
  })
}

// 开发板块饼图数据
export function getPiePicture(params) {
  return request({
    url: '/BigScreen/PiePicture',
    method: 'get',
    params: params,
  })
}

// 申请板块 今日需求数据二选一弹窗
export function getTodayNeed(params) {
  return request({
    url: '/BigScreen/getTodayNeed',
    method: 'get',
    params: params,
  })
}

// 申请板块 回流数据申请结果 三选一弹窗
export function getBackData(params) {
  return request({
    url: '/BigScreen/getBackData',
    method: 'get',
    params: params,
  })
}

// 申请板块 批量数据申请结果三选一弹窗
export function getBatchData(params) {
  return request({
    url: '/BigScreen/getBatchData',
    method: 'get',
    params: params,
  })
}

// 申请板块 回流数据 今日申请数据弹窗接口
export function getTodayRuquestBackData(params) {
  return request({
    url: '/BigScreen/getTodayRuquestBackData',
    method: 'get',
    params: params,
  })
}

// 申请板块 统计  批量数据申请结果和回流数据申请结果
export function getBacthAndBackData(params) {
  return request({
    url: '/BigScreen/CountBacthAndBackDataRequest',
    method: 'get',
    params: params,
  })
}

// 申请板块 分别统计 今日和累计 需求和申请 的统计结果
export function getCountTodayAndTotal(params) {
  return request({
    url: '/BigScreen/CountTodayAndTotal',
    method: 'get',
    params: params,
  })
}

// 申请模块 今日申请-批量数据
export function getTodayRequestBatchData(params) {
  return request({
    url: '/BigScreen/getTodayRequestBatchData',
    method: 'get',
    params: params,
  })
}

// 数据资源目录搜索列表
export function getSJZYMLList(params) {
  return request({
    url: 'BigScreenSearch/sjzymlSearch/list',
    method: 'get',
    params,
  })
}

// 数据资源目录搜索获取事件详情
export function getSJZYMLDetail(id) {
  return request({
    url: 'BigScreenSearch/sjzymlSearch/' + id,
    method: 'get',
  })
}

// 搜索界面统计库表、接口、总数
export function getSJZYMLCount(params) {
  return request({
    url: 'BigScreenSearch/sjzymlSearch/count',
    method: 'get',
    params,
  })
}

// 质量板块 柱状图 质量问题信息2
export function getQualityInfos2(params) {
  return request({
    url: 'BigScreen/qualityInfos2',
    method: 'get',
    params,
  })
}

// 柱状图 质量问题信息2 点击获得的清单
export function getQualityInfo(params) {
  return request({
    url: 'BigScreen/getqualityInfo',
    method: 'get',
    params,
  })
}

// 新 开发-柱状图
export function getDevData(params) {
  return request({
    url: 'BigScreen/getDevData',
    method: 'get',
    params
  })
}

// 新 质量-柱状图
export function getQualityData(params) {
  return request({
    url: 'BigScreen/getQualityData',
    method: 'get',
    params
  })
}

// 获取指标使用列表数据
export function getUseDataForZb(params) {
  return request({
    url: 'BigScreen/getUseDataForZb',
    method: 'get',
    params
  })
}
