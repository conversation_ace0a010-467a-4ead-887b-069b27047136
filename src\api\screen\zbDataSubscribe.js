import request from '@/utils/request'

// 获取指标信息列表
export function getSubscribeList(query) {
  return request({
    url: '/screen/zbSubscribe/getSubscribeList',
    method: 'post',
    data: query
  })
}
// 添加订阅
export function addZbSubscribe(query) {
  return request({
    url: '/screen/zbSubscribe/addZbSubscribe',
    method: 'post',
    data: query
  })
}
// 批量添加订阅
export function batchAddzbSubscribe(query) {
  return request({
    url: '/screen/zbSubscribe/batchAddzbSubscribe',
    method: 'post',
    data: query
  })
}
// 关闭订阅
export function removeZbSubscribe(query) {
  return request({
    url: '/screen/zbSubscribe/removeZbSubscribe',
    method: 'post',
    data: query
  })
}
