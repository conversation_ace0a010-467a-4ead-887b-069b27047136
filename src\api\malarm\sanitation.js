import request from '@/utils/request'

/**
 * 获取所有预警模型数据
 */
export function getWarningEnvironmentListAllModel(query) {
  return request({
    url: '/warningEnvironment/listAllModel',
    method: 'get',
    params: query,
  })
}

/**
 * 查询环卫设施安全运行预测预警列表
 */
export function getWarningEnvironmentList(query) {
  return request({
    url: '/warningEnvironment/list',
    method: 'get',
    params: query,
  })
}

/**
 * 导出环卫设施安全运行预测预警列表
 */
export function exportWarningEnvironmentList(data) {
  return request({
    url: '/warningEnvironment/export',
    method: 'post',
    data,
  })
}

/**
 * 获取环卫设施安全运行预测预警详细信息
 */
export function getWarningEnvironmentById(id) {
  return request({
    url: `/warningEnvironment/${id}`,
    method: 'get',
  })
}

/**
 * 新增环卫设施安全运行预测预警
 */
export function addWarningEnvironment(data) {
  return request({
    url: '/warningEnvironment',
    method: 'post',
    data,
  })
}

/**
 * 修改环卫设施安全运行预测预警
 */
export function updateWarningEnvironment(data) {
  return request({
    url: '/warningEnvironment',
    method: 'put',
    data,
  })
}

/**
 * 删除环卫设施安全运行预测预警
 */
export function deleteWarningEnvironment(ids) {
  return request({
    url: `/warningEnvironment/${ids}`,
    method: 'delete',
  })
}
