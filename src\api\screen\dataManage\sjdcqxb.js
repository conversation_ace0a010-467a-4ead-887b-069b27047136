import request from '@/utils/request'

// 查询数据导出清单列表
export function listSjdcqxb(query) {
  return request({
    url: '/sjdcqx/sjdcqxb/list',
    method: 'get',
    params: query
  })
}

// 查询数据导出清单详细
export function getSjdcqxb(id) {
  return request({
    url: '/sjdcqx/sjdcqxb/' + id,
    method: 'get'
  })
}

// 新增数据导出清单
export function addSjdcqxb(data) {
  return request({
    url: '/sjdcqx/sjdcqxb',
    method: 'post',
    data: data
  })
}

// 修改数据导出清单
export function updateSjdcqxb(data) {
  return request({
    url: '/sjdcqx/sjdcqxb',
    method: 'put',
    data: data
  })
}

// 删除数据导出清单
export function delSjdcqxb(id) {
  return request({
    url: '/sjdcqx/sjdcqxb/' + id,
    method: 'delete'
  })
}

// 导出数据导出清单
export function exportSjdcqxb(query) {
  return request({
    url: '/sjdcqx/sjdcqxb/export',
    method: 'get',
    params: query
  })
}