import request from '@/utils/request'

// 查询指标类型列表
export function listType(query) {
  return request({
    url: '/indicator/type/list',
    method: 'get',
    params: query,
  })
}

// 查询指标类型详细
export function getType(id) {
  return request({
    url: '/indicator/type/' + id,
    method: 'get',
  })
}

// 新增指标类型
export function addType(data) {
  return request({
    url: '/indicator/type',
    method: 'post',
    data: data,
  })
}

// 修改指标类型
export function updateType(data) {
  return request({
    url: '/indicator/type',
    method: 'put',
    data: data,
  })
}

// 删除指标类型
export function delType(id) {
  return request({
    url: '/indicator/type/' + id,
    method: 'delete',
  })
}
