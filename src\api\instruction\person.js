import request from '@/utils/request'

// 下载导入人员模板
export function importTemplate() {
  return request({
    url: '/instruction/person/importTemplate',
    method: 'post',
    responseType: 'blob',
  })
}

// 批量上传关联人员接口
export function importData(data) {
  return request({
    url: '/instruction/person/importData',
    method: 'post',
    data,
  })
}

// 通过关联人员ids分页查询关联人员信息
export function getPersonsByIds(params) {
  return request({
    url: '/instruction/person/selectPersonListByIds',
    method: 'get',
    params,
  })
}

// 查询指令关联人员信息列表
export function listPerson(query) {
  return request({
    url: '/instruction/person/list',
    method: 'get',
    params: query,
  })
}

// 查询指令关联人员信息详细
export function getPerson(id) {
  return request({
    url: '/instruction/person/' + id,
    method: 'get',
  })
}

// 新增指令关联人员信息
export function addPerson(data) {
  return request({
    url: '/instruction/person',
    method: 'post',
    data: data,
  })
}

// 修改指令关联人员信息
export function updatePerson(data) {
  return request({
    url: '/instruction/person',
    method: 'put',
    data: data,
  })
}

// 删除指令关联人员信息
export function delPerson(id) {
  return request({
    url: '/instruction/person/' + id,
    method: 'delete',
  })
}

// 根据人员姓名模糊查询人员列表
export function findPerson(query) {
  return request({
    url: '/instruction/person/selectPersonByName',
    method: 'get',
    params: query,
  })
}

// 根据参数查询地址薄
export function getPersonPlace(query) {
  return request({
    url: '/instruction/person/getPersonPlace',
    method: 'get',
    params: query,
  })
}

// 重点人员页面导入人员模板下载接口
export function importPersonTemplate(query) {
  return request({
    url: 'instruction/person/importPersonTemplate',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 重点人员页面导出接口
export function exportPersons(query) {
  return request({
    url: 'instruction/person/export',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 重点人员页面导入接口
export function uploadPersons(query) {
  return request({
    url: 'instruction/person/importPersonData',
    method: 'post',
  })
}

// 管控人员列表
export function personControlList(params) {
  return request({
    url: 'instruction/personControl/list',
    method: 'get',
    params,
  })
}

// 新增管控人员
export function addPersonControl(data) {
  return request({
    url: 'instruction/personControl',
    method: 'post',
    data,
  })
}

// 编辑管控人员
export function updatePersonControl(data) {
  return request({
    url: 'instruction/personControl',
    method: 'put',
    data,
  })
}

// 管控人员详情
export function getPersonControl(id) {
  return request({
    url: 'instruction/personControl/' + id,
    method: 'get',
  })
}

// 管控人员详情
export function deletePersonControl(id) {
  return request({
    url: 'instruction/personControl/' + id,
    method: 'delete',
  })
}

// 管控人员详情
export function exportPersonControl(data) {
  return request({
    url: 'instruction/personControl/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}
