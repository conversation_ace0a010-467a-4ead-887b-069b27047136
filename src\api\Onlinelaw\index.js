import request from '@/utils/request'

// 查询执法设备列(用于首页配套资源)列表
export function listOnlinelaw(query) {
  return request({
    url: '/xzzfj/lwzfjly/list',
    method: 'get',
    params: query,
  })
}

// 修改执法设备列(用于首页配套资源)
export function updateOnlinelaw(data) {
  return request({
    url: '/xzzfj/lwzfjly',
    method: 'put',
    data: data,
  })
}

// 部门下拉数据
export function getDepartment(query) {
  return request({
    url: '/xzzfj/dutyPersonnel/getDeptList',
    method: 'get',
    params: query
  })
}

// 获取用户区域
export function getAreaName() {
  return request({
    url: '/xzzfj/xzzfjZfsb/getArea',
    method: 'get',
  })
}
