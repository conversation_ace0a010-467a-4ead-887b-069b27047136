import request from '@/utils/request'

// 查询指令基本信息列表
// export function listInfo(query) {
//   return request({
//     url: '/jazzInstruction/info/list',
//     method: 'get',
//     params: query,
//   })
// }

// 获取指令列表信息
export function listInfo(params) {
  return request({
    url: '/jazzInstruction/info/testInstructionListNew',
    method: 'get',
    params: params,
  })
}
// export function listInfo(query) {
//   return request({
//     // url: '/instruction/info/testInstructionList',
//     url: '/instruction/info/testInstructionListNew',
//     method: 'get',
//     params: query,
//   })
// }

// 查询指令基本信息详细
export function getInfo(id) {
  return request({
    url: '/jazzInstruction/info/' + id,
    method: 'get',
  })
}

// 新增指令基本信息
export function addInfo(data) {
  return request({
    url: '/jazzInstruction/info',
    method: 'post',
    data: data,
  })
}

// 修改指令基本信息
export function updateInfo(data) {
  return request({
    url: '/jazzInstruction/info',
    method: 'put',
    data: data,
  })
}

// 删除指令基本信息
export function delInfo(id) {
  return request({
    url: '/jazzInstruction/info/' + id,
    method: 'delete',
  })
}

// 根据指令id获取指令流程相关信息
export function getInfoStream(id, type) {
  return request({
    // url: '/instruction/info/getProcessById/' + id,
    url: '/jazzInstruction/info/testGetProcessById/' + id + '/' + type,
    method: 'get',
    timeout: 10 * 1000 * 6,
  })
}

// 新增指令基本信息 - 提交流程接口
export function submitInfo(data) {
  return request({
    // url: '/instruction/info/submit',
    url: '/jazzInstruction/info/testSubmit',
    method: 'post',
    data: data,
  })
}

// 查询用户同级部门及以下部门
export function getDept(params) {
  return request({
    url: '/system/user/getDept',
    method: 'get',
    params: params,
  })
}

// 待办红点统计 - 查询出人员待办事件
export function getStatistics(params) {
  return request({
    url: '/jazzInstruction/info/testRedStatistics',
    method: 'get',
    params: params,
  })
}
