import request from '@/utils/request'

/**
 * 查询任务规则列表
 */
export function getTaskRuleList(params) {
  return request({
    url: '/zhcg/task/rule/list',
    method: 'get',
    params,
  })
}

/**
 * 导出任务规则列表
 */
export function exportTaskRuleList(params) {
  return request({
    url: '/zhcg/task/rule/export',
    method: 'post',
    params,
    responseType: 'blob',
  })
}

/**
 * 获取任务规则详细信息
 */
export function getTaskRuleDetail(id) {
  return request({
    url: `/zhcg/task/rule/${id}`,
    method: 'get',
  })
}

/**
 * 新增任务规则
 */
export function addTaskRule(data) {
  return request({
    url: '/zhcg/task/rule',
    method: 'post',
    data,
  })
}

/**
 * 修改任务规则
 */
export function updateTaskRule(data) {
  return request({
    url: '/zhcg/task/rule',
    method: 'put',
    data,
  })
}

/**
 * 修改启用状态
 */
export function updateTaskRuleStatus(data) {
  return request({
    url: '/zhcg/task/rule/updateStatus',
    method: 'post',
    data,
  })
}

/**
 * 删除任务规则
 */
export function deleteTaskRule(ids) {
  return request({
    url: `/zhcg/task/rule/${ids}`,
    method: 'put',
  })
}

/**
 * 获取催办规则
 */
export function getTaskRemind(params) {
  return request({
    url: '/zhcg/task/remind',
    method: 'get',
    params,
  })
}

/**
 * 保存催办规则
 */
export function saveTaskRemind(data) {
  return request({
    url: '/zhcg/task/remind',
    method: 'post',
    data,
  })
}
