import request from '@/utils/request'

// 查询回访分析
export function getHffx(params) {
  return request({
    url: '/ajhf/zftsZfrw/followAnalysis',
    method: 'get',
    params
  })
}

// 查询全量案件
export function getQlajList(params) {
  return request({
    url: '/ajhf/zftsZfrw/qlList',
    method: 'get',
    params
  })
}

// 查询发送失败案件total
export function getFailListTotal(params) {
  return request({
    url: '/ajhf/zftsZfrw/failTotal',
    method: 'get',
    params
  })
}

// 设置回访操作
export function setHfType(data) {
  return request({
    url: '/ajhf/zftsZfrw/batchSetFollow',
    method: 'post',
    data
  })
}

//全量案件区县分布
export function getQlajQxfb(params) {
  return request({
    url: '/ajhf/zftsZfrw/xqfb',
    method: 'get',
    params
  })
}

//修改回访类型
export function updateType(data) {
  return request({
    url: '/ajhf/zftsZfrw/updateFollowType',
    method: 'post',
    data
  })
}

//修改联系方式
export function updatePhone(data) {
  return request({
    url: '/ajhf/zftsZfrw/updatePhone',
    method: 'post',
    data
  })
}

//图片转url
export function uploadFile(caseNo,data) {
  return request({
    url: `/ajhf/hfQuestionnaireH5/uploadImg/${caseNo}`,
    method: 'post',
    headers:{
      'Content-Type':'multipart/form-data'
    },
    data
  })
}

//自行回访
export function postForm(data) {
  return request({
    url: '/ajhf/hfQuestionnaire',
    method: 'post',
    data
  })
}

//问题统计列表
export function getWttjList(params) {
  return request({
    url: '/ajhf/zftsZfrw/wttjList',
    method: 'get',
    params
  })
}

//问题统计数量列表
export function getWttjNumberList(params) {
  return request({
    url: '/ajhf/zftsZfrw/wttjsl',
    method: 'get',
    params
  })
}

//详情
export function getDetail(params) {
  return request({
    url: '/ajhf/zftsZfrw/getDetails',
    method: 'get',
    params
  })
}

//回访分析指标
export function getfollowAnalysis(params) {
  return request({
    url: '/ajhf/zftsZfrw/hfStatistics',
    method: 'get',
    params
  })
}

//回访管理图表
export function getHfglChart(params) {
  return request({
    url: '/ajhf/zftsZfrw/hfxqfb',
    method: 'get',
    params
  })
}

//部门列表
export function getDepartList() {
  return request({
    url: '/ajhf/ajhfLzCz/lzDept',
    method: 'post'
  })
}

//回访管理列表
export function getHfglList(params) {
  return request({
    url: '/ajhf/zftsZfrw/hfList',
    method: 'get',
    params
  })
}

//认领
export function renling(data) {
  return request({
    url: '/ajhf/zftsZfrw/claim',
    method: 'post',
    data
  })
}

//采纳
export function caina(data) {
  return request({
    url: '/ajhf/zftsZfrw/opinionAdoption',
    method: 'post',
    data
  })
}

//流转处置
export function LiuzChuz(data) {
  return request({
    url: '/ajhf/ajhfLzCz/lzcz',
    method: 'post',
    data
  })
}

//回访处置列表
export function getHfczList(params) {
  return request({
    url: '/ajhf/zftsZfrw/hflzczList',
    method: 'get',
    params
  })
}

//预警管理列表
export function getyjglList(params) {
  return request({
    url: '/ajhf/zftsZfrw/cqList',
    method: 'get',
    params
  })
}

//预警管理指标
export function getyjglAnalysis(params) {
  return request({
    url: '/ajhf/zftsZfrw/cqTj',
    method: 'get',
    params
  })
}

//预警管理图表
export function getyjglChart(params) {
  return request({
    url: '/ajhf/zftsZfrw/cqXqfb',
    method: 'get',
    params
  })
}

//预警配置列表
export function getyjpzList(params) {
  return request({
    url: '/ajhf/cqyjXsqPz/list',
    method: 'get',
    params
  })
}

//预警配置列表详情
export function getyjpzDetail(params) {
  return request({
    url: '/ajhf/cqyjXsqPzLog/list',
    method: 'get',
    params
  })
}

//修改预警状态
export function setYjStatus(data) {
  return request({
    url: '/ajhf/cqyjXsqPz',
    method: 'put',
    data
  })
}

//导出
export function ouputFileList(params) {
  return request({
    url: '/ajhf/zftsZfrw/qlListExport',
    method: 'get',
    responseType: 'blob',
    params
  })
}

//导出
export function ManageOuputFile(params) {
  return request({
    url: '/ajhf/zftsZfrw/hfListExport',
    method: 'get',
    responseType: 'blob',
    params
  })
}

//获取号码
export function getPhoneNumber(caseNo) {
  return request({
    url: `/ajhf/zftsZfrw/getPhone/${caseNo}`,
    method: 'get'
  })
}

//领导视图基础信息
export function getBaseList() {
  return request({
    url: `/ajhf/ldst/jbxxtj`,
    method: 'get'
  })
}

//领导视图基础信息折线图
export function getBaseListChart(params) {
  return request({
    url: `/ajhf/ldst/jbxxtjzxt`,
    method: 'get',
    params
  })
}

//回访排名柱图
export function gethfRangeBar(params) {
  return request({
    url: `/ajhf/ldst/ldstHfpm`,
    method: 'get',
    params
  })
}

//回访分析饼图
export function gethfRangePie() {
  return request({
    url: `/ajhf/ldst/ldstHffx`,
    method: 'get'
  })
}

//回访跟踪
export function gethfgzTable(params) {
  return request({
    url: `/ajhf/ldst/ldstHfzz`,
    method: 'get',
    params
  })
}

//认领跟踪
export function getrlgzTable(params) {
  return request({
    url: `/ajhf/ldst/ldstRlgz`,
    method: 'get',
    params
  })
}

//认领跟踪统计
export function getrlgztj() {
  return request({
    url: `/ajhf/ldst/ldstRlgzTj`,
    method: 'get'
  })
}

//回访单模块管理列表  title（String）：标题  status（String）：状态 0停用 1启用
export function getHfdList(params) {
  return request({
    url: `/questionnaire/list`,
    method: 'get',
    params
  })
}

//更新回访单模块状态
export function updateHfdStatus(data) {
  return request({
    url: `/questionnaire`,
    method: 'put',
    data
  })
}

//保存回访单模块
export function saveHfd(data) {
  return request({
    url: `/questionnaire`,
    method: 'post',
    data
  })
}

//获取回访单模块详情
export function getHfdDetail(id) {
  return request({
    url: `/questionnaire/${id}`,
    method: 'get'
  })
}

//删除回访单模块
export function deleteHfd(id) {
  return request({
    url: `/questionnaire/${id}`,
    method: 'delete'
  })
}

//获取回访比例
export function getHfConfig(id) {
  return request({
    url: `/szzfj/tZftsHfbisz/list`,
    method: 'get'
  })
}

//回访比例修改
export function updateHfConfig(data) {
  return request({
    url: `/szzfj/tZftsHfbisz/editList`,
    method: 'post',
    data
  })
}

//统计导出
export function wttjOuputFileList(params) {
  return request({
    url: '/ajhf/zftsZfrw/wttjListExport',
    method: 'get',
    responseType: 'blob',
    params
  })
}
