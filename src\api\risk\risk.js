import request from '@/utils/request'

// 查询事件基本信息列表
export function listItem(query) {
  return request({
    url: '/item/info/list',
    method: 'get',
    params: query,
  })
}

// 查询事件基本信息详细
export function getItem(id) {
  return request({
    url: '/item/info/' + id,
    method: 'get',
  })
}
// 责任单位
export function getZrdw() {
  return request({
    url: '/system/dict/data/type/zl_department_received',
  })
}

// 新增事件基本信息
export function addItem(data) {
  return request({
    url: '/item/info',
    method: 'post',
    data: data,
  })
}

// 修改事件基本信息
export function updateItem(data) {
  return request({
    url: '/item/info',
    method: 'put',
    data: data,
  })
}

// 删除事件基本信息
export function delItem(id) {
  return request({
    url: '/item/info/' + id,
    method: 'delete',
  })
}

// 导出事件数据接口
export function exportItems(query) {
  return request({
    url: '/item/info/export',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 通过关联人员ids分页查询关联人员信息
export function getItemListById(id) {
  return request({
    // url: '/instruction/person/selectPersonListByIds/' + id,
    url: '/jazzInstruction/event/getEventListByGroupId/' + id,
    method: 'get',
  })
}

// 查询群体基本信息列表
// export function listGroup(query) {
//   return request({
//     url: '/instruction/group/list',
//     method: 'get',
//     params: query,
//   })
// }

// 根据群体id查询相关联事件
// export function getEventListByGroupId(id) {
//   return request({
//     url: '/instruction/event/getEventListByGroupId/' + id,
//     method: 'get',
//   })
// }

// 导入事件模板下载接口
export function importEventTemplate(query) {
  return request({
    url: '/item/info/importPersonTemplate',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 导出事件数据接口
// export function exportEvents(query) {
//   return request({
//     url: 'instruction/event/export',
//     method: 'post',
//     params: query,
//     responseType: 'blob',
//   })
// }

// 导入事件数据接口
// export function uploadEvent(query) {
//   return request({
//     url: 'instruction/event/importData',
//     method: 'post',
//   })
// }
