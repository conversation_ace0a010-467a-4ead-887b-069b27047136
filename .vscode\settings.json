{
  "eslint.options": {
    // "extensions": [ ".html", ".js", ".vue", ".jsx" ]
    // "plugins": ["html"]
  },
  "eslint.validate": [
    "javascript",
    "typescript",
    "javascriptreact",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "json5"
  ],
  "editor.codeActionsOnSave": {


    "source.fixAll.eslint": "explicit",
    "source.fixAll.tslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.organizeImports": "never"
  },
  "eslint.quiet": true,
}
