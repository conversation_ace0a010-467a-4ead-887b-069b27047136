import request from '@/utils/request'

// 查询城市大脑数据申请台帐列表
export function listSjsqtz(query) {
  return request({
    url: '/sjsqtz/sjsqtz/list',
    method: 'get',
    params: query
  })
}

// 查询城市大脑数据申请台帐详细
export function getSjsqtz(id) {
  return request({
    url: '/sjsqtz/sjsqtz/' + id,
    method: 'get'
  })
}

// 新增城市大脑数据申请台帐
export function addSjsqtz(data) {
  return request({
    url: '/sjsqtz/sjsqtz',
    method: 'post',
    data: data
  })
}

// 修改城市大脑数据申请台帐
export function updateSjsqtz(data) {
  return request({
    url: '/sjsqtz/sjsqtz',
    method: 'put',
    data: data
  })
}

// 删除城市大脑数据申请台帐
export function delSjsqtz(id) {
  return request({
    url: '/sjsqtz/sjsqtz/' + id,
    method: 'delete'
  })
}

// 导出城市大脑数据申请台帐
export function exportSjsqtz(query) {
  return request({
    url: '/sjsqtz/sjsqtz/export',
    method: 'get',
    params: query
  })
}

// 查询项目下拉列表
export function getXmList() {
  return request({
    url: '/sjsqtz/sjsqtz/sjtz/getXmList',
    method: 'get'
  })
}

// 查询所属领域下拉列表
export function getSourceList() {
  return request({
    url: '/sjsqtz/sjsqtz/sjtz/getSslyList',
    method: 'get'
  })
}

// 模板下载
export function getTemp() {
  return request({
    url: '/sjsqtz/sjsqtz/sjtz/importTemplate',
    method: 'get'
  })
}

// 总师同意
export function zsty(id) {
  return request({
    url: `/sjsqtz/sjsqtz/zsqrty/${id}`,
    method: 'post'
  })
}

// 总师不同意
export function zsbty(data) {
  return request({
    url: `/sjsqtz/sjsqtz/zsqrbty`,
    method: 'post',
    data
  })
}

// 经办人确认
export function jbrqr(data) {
  return request({
    url: `/sjsqtz/sjsqtz/jbrqr`,
    method: 'post',
    data
  })
}

//数据台账 获取数源单位
export function getSydwList() {
  return request({
    url: '/sjsqtz/sjsqtz/sjtz/sydw',
    method: 'get'
  })
}
// 申请结果同意
export function sqjgty(id) {
  return request({
    url: `/sjsqtz/sjsqtz/sqjgty/${id}`,
    method: 'post'
  })
}

// 申请结果不同意
export function sqjgbty(id) {
  return request({
    url: `/sjsqtz/sjsqtz/sqjgbty/${id}`,
    method: 'post'
  })
}
