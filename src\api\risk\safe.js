import request from '@/utils/request'

// 查询平安金华区域统计列表
export function areaList(query) {
  return request({
    url: '/instruction/town/list',
    method: 'get',
    params: query,
  })
}

// 查询平安金华区域统计列表
export function listSafe(query) {
  return request({
    url: '/instruction/areaStatistics/list',
    method: 'get',
    params: query,
  })
}

// 导出平安金华区域统计列表
export function exportSafe(query) {
  return request({
    url: '/instruction/areaStatistics/export',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 获取平安金华区域统计信息详细
export function getSafe(id) {
  return request({
    url: '/instruction/areaStatistics/' + id,
    method: 'get',
  })
}

// 新增平安金华区域统计
export function addSafe(data) {
  return request({
    url: '/instruction/areaStatistics/addList',
    method: 'post',
    data: data,
  })
}

// 修改平安金华区域统计
export function updateSafe(data) {
  return request({
    url: '/instruction/areaStatistics',
    method: 'put',
    data: data,
  })
}

// 删除平安金华区域统计
export function delSafe(id) {
  return request({
    url: '/instruction/areaStatistics/' + id,
    method: 'delete',
  })
}

// 导入事件模板下载接口
export function importSafeTemplate(query) {
  return request({
    // url: 'jazzInstruction/event/importTemplate',
    url: '/instruction/areaStatistics/importPersonTemplate',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}
