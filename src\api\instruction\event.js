import request from '@/utils/request'

// 查询事件基本信息列表
export function listEvent(query) {
  return request({
    url: '/instruction/event/list',
    method: 'get',
    params: query,
  })
}

// 查询事件基本信息详细
export function getEvent(id) {
  return request({
    url: '/instruction/event/' + id,
    method: 'get',
  })
}

// 新增事件基本信息
export function addEvent(data) {
  return request({
    url: '/instruction/event',
    method: 'post',
    data: data,
  })
}

// 修改事件基本信息
export function updateEvent(data) {
  return request({
    url: '/instruction/event',
    method: 'put',
    data: data,
  })
}

// 删除事件基本信息
export function delEvent(id) {
  return request({
    url: '/instruction/event/' + id,
    method: 'delete',
  })
}

// 查询群体基本信息列表
export function listGroup(query) {
  return request({
    url: '/instruction/group/list',
    method: 'get',
    params: query,
  })
}

// 根据群体id查询相关联事件
export function getEventListByGroupId(id) {
  return request({
    url: '/instruction/event/getEventListByGroupId/' + id,
    method: 'get',
  })
}
export function getEventListByGroupId1(id) {
  return request({
    url: '/jazzInstruction/event/getEventListByGroupId/' + id,
    method: 'get',
  })
}

// 导入事件模板下载接口
export function importEventTemplate(query) {
  return request({
    url: 'instruction/event/importTemplate',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 导出事件数据接口
export function exportEvents(query) {
  return request({
    url: 'instruction/event/export',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 导入事件数据接口
export function uploadEvent(query) {
  return request({
    url: 'instruction/event/importData',
    method: 'post',
  })
}
