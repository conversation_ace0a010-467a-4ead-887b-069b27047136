<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="value"
    width="50%"
    :before-close="handleClose"
    append-to-body
    class="selectUserDialog"
    @open="handleOpen"
  >
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :span="6" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" style="height: 360px; overflow-y: auto">
          <el-tree
            ref="deptTree"
            node-key="id"
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterDeptNode"
            highlight-current
            default-expand-all
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>

      <!-- 用户列表 -->
      <el-col :span="18" :xs="24">
        <el-form :inline="true" size="small">
          <el-form-item label="用户名称">
            <el-input
              v-model="searchValue"
              placeholder="请输入用户名称"
              clearable
              @keyup.enter.native="searchUsers"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchUsers">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          ref="multipleTable"
          v-loading="loading"
          :data="userList"
          style="width: 100%; margin-top: 8px; height: 300px"
          row-key="userId"
          max-height="360px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" :reserve-selection="true" />
          <el-table-column label="用户名称" prop="userName" align="center" />
          <el-table-column label="用户昵称" prop="nickName" align="center" />
          <el-table-column label="部门" prop="dept.deptName" align="center" />
          <el-table-column label="手机号码" prop="phonenumber" align="center" width="120" />
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { listUser } from '@/api/system/user'
import { zqTreeselect } from '@/api/system/dept'
import Pagination from '@/components/Pagination'

export default {
  name: 'NewUserSelect',
  components: {
    Pagination
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    selectUserKeys: {
      type: Array,
      default: () => []
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => ['100']
    },
    multiple: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      title: '选择人员',
      loading: false,
      // 部门名称
      deptName: '',
      // 部门树选项
      deptOptions: undefined,
      // 用户表格数据
      userList: [],
      // 选中的用户对象数组
      selectedUsers: [],
      // 总条数
      total: 0,
      // 默认属性
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        deptId: undefined
      },
      // 搜索关键字
      searchValue: ''
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.deptTree.filter(val)
    },
    // 监听传入的选中用户IDs
    selectUserKeys: {
      handler(val) {
        if (!this.value) {
          return
        }
        if (val && val.length > 0) {
          // 初始化已选择的用户
          this.$nextTick(() => {
            // 如果表格已加载，则预选中数据
            if (this.$refs.multipleTable && this.userList.length > 0) {
              this.userList.forEach(row => {
                if (val.includes(String(row.userId))) {
                  this.$refs.multipleTable.toggleRowSelection(row, true)
                }
              })
            }
          })
        } else {
          this.$nextTick(() => {
            this.$refs.multipleTable.clearSelection()
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    // 处理打开对话框
    handleOpen() {
      this.loading = true
      this.getTreeselect()
    },

    // 获取部门树结构
    getTreeselect() {
      zqTreeselect().then(response => {
        this.deptOptions = response.data[0].children
        this.loading = false

        // 默认选中第一个部门并加载其用户
        this.$nextTick(() => {
          if (this.deptOptions && this.deptOptions.length > 0) {
            const firstDept = this.deptOptions[0]
            // 设置当前选中的部门ID
            this.queryParams.deptId = firstDept.id
            // 高亮显示选中的部门节点
            this.$refs.deptTree.setCurrentKey(firstDept.id)
            // 加载选中部门的用户列表
            this.getList()
          }
        })
      })
    },

    // 筛选部门节点
    filterDeptNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    // 部门树节点点击
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.getList()
    },

    // 获取用户列表
    getList() {
      this.loading = true
      listUser(this.queryParams).then(response => {
        this.userList = response.rows.map(item => ({
          ...item,
          userId: String(item.userId)
        }))
        this.total = response.total
        this.loading = false

        // 处理初始选中数据 - 仅在组件首次加载时执行
        if (this.selectUserKeys && this.selectUserKeys.length > 0 && !this.selectedUsers.length) {
          this.$nextTick(() => {
            // 预选中已有的用户
            console.log('selectUserKeys', this.selectUserKeys)
            this.selectUserKeys.forEach(userId => {
              const existUser = this.userList.find(u => String(u.userId) === String(userId))
              if (existUser) {
                this.$refs.multipleTable.toggleRowSelection(existUser, true)
              }
            })
          })
        }
      })
    },

    // 搜索用户
    searchUsers() {
      this.queryParams.nickName = this.searchValue
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.searchValue = ''
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        deptId: this.queryParams.deptId
      }
      this.getList()
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      if (!this.multiple && selection.length > 1) {
        // 单选模式下只保留最后选中的一项
        const lastSelected = selection[selection.length - 1]
        this.$refs.multipleTable.clearSelection()
        this.$refs.multipleTable.toggleRowSelection(lastSelected, true)
        this.selectedUsers = [lastSelected]
      } else {
        // 多选模式
        this.selectedUsers = selection
      }
    },

    // 确认选择
    handleConfirm() {
      // 获取所有选中用户的ID
      const userIds = this.selectedUsers.map(user => user.userId).join(',')

      // 构建名称字符串
      const userNames = this.selectedUsers.map(item => item.nickName).join(',')

      // 获取部门信息
      let parent = null
      if (this.selectedUsers.length > 0 && this.selectedUsers[0].dept) {
        parent = {
          id: this.selectedUsers.map(item => item.dept.deptId).join(','),
          label: this.selectedUsers.map(item => item.dept.deptName).join(',')
        }
      }

      // 发送选择结果
      this.$emit('confirm', {
        id: userIds,
        name: userNames,
        parent
      })

      this.handleClose()
    },

    // 关闭对话框
    handleClose() {
      this.$emit('input', false)
      // 重置状态
      this.userList = []
      // this.selectedUsers = []
      this.searchValue = ''
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        deptId: undefined
      }
    }
  }
}
</script>

<style>
.selectUserDialog .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
}
.head-container {
  padding: 0 10px;
}
</style>
