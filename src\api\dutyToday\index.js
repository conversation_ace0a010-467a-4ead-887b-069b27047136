import request from '@/utils/request'

// 查询今日值班信息-表格
export function getDutyTable(query) {
  return request({
    url: '/xzzfj/dutyInformation/listForArea',
    method: 'get',
    params: query,
  })
}

// 查询今日值班信息-列表
export function getDutyList(query) {
  return request({
    url: '/xzzfj/dutyPersonnel/listNotPage',
    method: 'get',
    params: query,
  })
}

// 新增或编辑今日值班信息
export function updateDutyPerson(data) {
  return request({
    url: '/xzzfj/dutyInformation/addAndUpdate',
    method: 'POST',
    data: data,
  })
}

// 删除今日值班信息
export function deleteDutyPerson(data) {
  return request({
    url: '/xzzfj/dutyInformation/delete',
    method: 'POST',
    data: data,
  })
}
