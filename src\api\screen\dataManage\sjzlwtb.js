import request from '@/utils/request'

// 查询城市大脑数据质量问题清单列表
export function listSjzlwtb(query) {
  return request({
    url: '/sjzlwt/sjzlwtb/list',
    method: 'get',
    params: query
  })
}

// 查询城市大脑数据质量问题清单详细
export function getSjzlwtb(id) {
  return request({
    url: '/sjzlwt/sjzlwtb/' + id,
    method: 'get'
  })
}

// 新增城市大脑数据质量问题清单
export function addSjzlwtb(data) {
  return request({
    url: '/sjzlwt/sjzlwtb',
    method: 'post',
    data: data
  })
}

// 修改城市大脑数据质量问题清单
export function updateSjzlwtb(data) {
  return request({
    url: '/sjzlwt/sjzlwtb',
    method: 'put',
    data: data
  })
}

// 删除城市大脑数据质量问题清单
export function delSjzlwtb(id) {
  return request({
    url: '/sjzlwt/sjzlwtb/' + id,
    method: 'delete'
  })
}

// 导出城市大脑数据质量问题清单
export function exportSjzlwtb(query) {
  return request({
    url: '/sjzlwt/sjzlwtb/export',
    method: 'get',
    params: query
  })
}

// 获取质量问题项目下拉列表
export function getxmList() {
  return request({
    url: '/sjzlwt/sjzlwtb/sjzlwt/getXmList',
    method: 'get'
  })
}
