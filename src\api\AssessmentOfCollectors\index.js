import request from '@/utils/request'

// 迟到早退模型保存
export function setAttendanceRule(data) {
  return request({
    url: '/collectors/late/SaveOrUpdate',
    method: 'post',
    data
  })
}

// 疑似摸鱼模型保存
export function setSysSlack(data) {
  return request({
    url: '/collectors/fish/Update',
    method: 'put',
    data
  })
}

// 查询迟到早退模型详情
export function getAttendanceRuleDetail(params) {
  return request({
    url: '/collectors/late/select',
    method: 'get',
    params
  })
}

// 查询疑似摸鱼模型详情
export function getSysSlackDetail() {
  return request({
    url: '/collectors/fish/select',
    method: 'get'
  })
}

// 查询部门列表
export function getDepartList() {
  return request({
    url: '/collectors/dept/street/select',
    method: 'get'
  })
}

// 查询树图详情
export function findCjy(params) {
  return request({
    url: '/collectors/config/select',
    method: 'get',
    params
  })
}

// 路段添加
export function roadAdd(data) {
  return request({
    url: '/collectors/config/add',
    method: 'post',
    data
  })
}

// 路段编辑
export function roadEdit(data) {
  return request({
    url: '/collectors/config/update',
    method: 'put',
    data
  })
}

// 路段删除
export function roadDelete(id) {
  return request({
    url: `/collectors/config/delete/${id}`,
    method: 'post'
  })
}

// 查询该街道下的检查员
export function getJcyList(params) {
  return request({
    url: `/collectors/street/collectors/select`,
    method: 'get',
    params
  })
}

// 人员调取
export function khry(params) {
  return request({
    url: '/collectors/attendance/map',
    method: 'get',
    params
  })
}

// 考核人员
export function rydetail(params) {
  return request({
    url: '/collectors/attendance/info',
    method: 'get',
    params
  })
}

// 考勤明细查询  startTime: 开始时间 endTime: 结束时间 nickName: 用户昵称
export function getKqmxList(params) {
  return request({
    url: '/collectors/attendance/select',
    method: 'get',
    params
  })
}

// 考勤明细导出
export function exportKqmxList(data) {
  return request({
    url: '/collectors/attendance/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 获取考勤详情
export function getKqmxDetail(id) {
  return request({
    url: `/collectors/attendance/detail/${id}`,
    method: 'get'
  })
}
