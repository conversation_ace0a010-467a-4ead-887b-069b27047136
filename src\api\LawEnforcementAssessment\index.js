import request from '@/utils/request'

// 查询树图列表
export function getTreeList() {
  return request({
    url: '/business/assess/config/list',
    method: 'get'
  })
}

// 树图添加
export function TreeListAdd(data) {
  return request({
    url: '/business/assess/config/add',
    method: 'post',
    data
  })
}

// 树图编辑
export function TreeListEdit(data) {
  return request({
    url: '/business/assess/config/edit',
    method: 'put',
    data
  })
}

// 查询表格内容
export function getTableData(params) {
  return request({
    url: '/business/assess/config/listText',
    method: 'get',
    params
  })
}

// 表格内容编辑
export function TableDataEdit(data) {
  return request({
    url: '/business/assess/config/edit',
    method: 'put',
    data
  })
}

// 删除
export function DataDelete(id) {
  return request({
    url: `/business/assess/config/${id}`,
    method: 'put'
  })
}


// 查询结果页树图列表
export function getResultTreeList() {
  return request({
    url: '/business/assess/results/list',
    method: 'get'
  })
}

// 查询结果页表格内容
export function getResultTableData(params) {
  return request({
    url: '/business/assess/results/listText',
    method: 'get',
    params
  })
}

// 查询结果页表格内容
export function ResultTableDataScoreEdit(data) {
  return request({
    url: '/business/assess/results/edit',
    method: 'put',
    data
  })
}

// 生成考核结果
export function getAssessmentResults(data) {
  return request({
    url: '/business/assess/results/submit',
    method: 'post',
    data
  })
}

// 考核结果详情
export function getAssessmentMakeOver(params) {
  return request({
    url: '/business/assess/overview/makeover',
    method: 'get',
    params
  })
}

// 考评总览列表
export function getOverviewList() {
  return request({
    url: '/business/assess/overview/list',
    method: 'get'
  })
}
