import request from '@/utils/request'

// 新增权限规则
export function addWeightRule (data) {
  return request({
    url: '/screen/indicatorBackWeightRule',
    method: 'post',
    data
  })
}

// 修改权限规则
export function editWeightRule (data) {
  return request({
    url: '/screen/indicatorBackWeightRule',
    method: 'put',
    data
  })
}

// 查询权限规则列表
export function getWeightRule (params) {
  return request({
    url: '/screen/indicatorBackWeightRule/list',
    method: 'get',
    params
  })
}
