import request from '@/utils/request'

// 查询案件列表
export function getAjList(params) {
  return request({
    url: '/zhcg/event/list',
    method: 'get',
    params,
    timeout: 10 * 1000 * 6,
  })
}

// 新增案件
export function addAj(data) {
  return request({
    url: '/zhcg/event',
    method: 'post',
    data,
  })
}

// 编辑案件
export function editAj(data) {
  return request({
    url: '/zhcg/event',
    method: 'put',
    data,
  })
}

// 获取案件受理详细信息
export function getAjDetail(id) {
  return request({
    url: `/zhcg/event/${id}`,
    method: 'get',
  })
}

// 反映人查询
export function fyrSearch(params) {
  return request({
    url: '/zhcg/reporter/list',
    method: 'get',
    params,
  })
}

// 新增举报人
export function addfyr(data) {
  return request({
    url: '/zhcg/reporter',
    method: 'post',
    data,
  })
}

// 修改举报人
export function editfyr(data) {
  return request({
    url: '/zhcg/reporter',
    method: 'put',
    data,
  })
}

// 反映人详情
export function getFyrDetail(id) {
  return request({
    url: `/zhcg/reporter/${id}`,
    method: 'get',
  })
}

// 监督员列表
export function getJdyList(params) {
  return request({
    url: `/system/user/getUserListByRoleId`,
    method: 'get',
    params,
  })
}

// 平台列表
export function getPlatList(params) {
  return request({
    url: `/system/dept/getList`,
    method: 'get',
    params,
  })
}

// 新增案件处理
export function addAjcl(data) {
  return request({
    url: '/zhcg/operate',
    method: 'post',
    data,
  })
}

// 指标接口
export function indexApi(id, params = {}) {
  return request({
    url: `/indexPort?indexid=${id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
      ptid: 'PT0001',
    },
    dataType: 'json',
    params,
  })
}

// 修改网格责任人
export function updateZRR(data) {
  return request({
    url: '/zhcg/tWg',
    method: 'post',
    data,
  })
}

// 查询网格责任人
export function getZRR(params) {
  return request({
    url: `/zhcg/tWg/infoDetails`,
    method: 'get',
    params,
  })
}

// 查询办理经过
export function getBljg(params) {
  return request({
    url: `/zhcg/operate/newList`,
    method: 'get',
    params,
  })
}

// 查询专项整治任务表列表
export function getSpecialTaskList(params) {
  return request({
    url: `/zhcg/specialTask/list`,
    method: 'get',
    params,
  })
}

// 查询专项整治任务详情
export function getSpecialTaskDetail(id) {
  return request({
    url: `/zhcg/specialTask/${id}`,
    method: 'get',
  })
}

// 新增专项整治任务
export function AddSpecialTask(data) {
  return request({
    url: `/zhcg/specialTask`,
    method: 'post',
    data,
  })
}

// 修改专项整治任务
export function EditSpecialTask(data) {
  return request({
    url: `/zhcg/specialTask`,
    method: 'put',
    data,
  })
}

// 普查/整治列表 查询条件字段  开始时间: searchStartTime 结束时间: searchEndTime 问题描述: problemDesc  任务编号: taskId isShow： 1整治 2普查
export function getPcList(params) {
  return request({
    url: `/zhcg/event/list`,
    method: 'get',
    params,
    timeout: 10 * 1000 * 6,
  })
}

/**
 * 获取案卷配置管理列表
 */
export function getEventConfig(params) {
  return request({
    url: `/zhcg/event/getEventConfig`,
    method: 'get',
    params,
  })
}

/**
 * 账号上线/下线操作
 */
export function upUnderLine(params) {
  return request({
    url: `/zhcg/event/upUnderLine`,
    method: 'put',
    params,
  })
}

/**
 * 导出表格
 */
export function exportEventConfig(data) {
  return request({
    url: `/zhcg/event/exportEventConfig`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 普查任务导出接口
export function exportGeneralSurveyList(data) {
  return request({
    url: `/zhcg/event/GeneralExport`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 整治任务导出接口
export function exportRectificationList(data) {
  return request({
    url: `/zhcg/event/RectificationExport`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// ai分析
export function AiAnalysisEvent(data) {
  return request({
    url: `/zhcg/event/analysisEvent`,
    method: 'post',
    data,
  })
}

/**
 * 类型、大类、小类、立案标准列表
 */
export function getSearchType(params) {
  return request({
    url: `/zhcg/event/searchType`,
    method: 'get',
    params,
  })
}

// 退回 传参 id, eventType（固定传字符串1）
export function turnDown(params) {
  return request({
    url: `/zhcg/event/turnDown`,
    method: 'get',
    params,
  })
}

// 案件状态下拉列表
export function getStatusList() {
  return request({
    url: `/system/dict/data/getPullBox`,
    method: 'get',
  })
}
