import request from '@/utils/request'

/**
 * 查询人员密集场所安全运行预测预警列表
 */
export function getWarningPersonList(query) {
  return request({
    url: '/warningPerson/list',
    method: 'get',
    params: query,
  })
}

/**
 * 导出人员密集场所安全运行预测预警列表
 */
export function exportWarningPersonList(data) {
  return request({
    url: '/warningPerson/export',
    method: 'post',
    data,
  })
}

/**
 * 获取人员密集场所安全运行预测预警详细信息
 */
export function getWarningPersonById(id) {
  return request({
    url: `/warningPerson/${id}`,
    method: 'get',
  })
}

/**
 * 新增人员密集场所安全运行预测预警
 */
export function addWarningPerson(data) {
  return request({
    url: '/warningPerson',
    method: 'post',
    data,
  })
}

/**
 * 修改人员密集场所安全运行预测预警
 */
export function updateWarningPerson(data) {
  return request({
    url: '/warningPerson',
    method: 'put',
    data,
  })
}

/**
 * 删除人员密集场所安全运行预测预警
 */
export function deleteWarningPerson(ids) {
  return request({
    url: `/warningPerson/${ids}`,
    method: 'delete',
  })
}
