import request from '@/utils/request'

// 查询执法设备列(用于首页配套资源)列表
export function listXzzfjZfsb(query) {
  return request({
    url: '/xzzfj/xzzfjZfsb/list',
    method: 'get',
    params: query,
  })
}

// 查询执法设备列(用于首页配套资源)详细
export function getXzzfjZfsb(id) {
  return request({
    url: '/xzzfj/xzzfjZfsb/' + id,
    method: 'get',
  })
}

// 新增执法设备列(用于首页配套资源)
export function addXzzfjZfsb(data) {
  return request({
    url: '/xzzfj/xzzfjZfsb',
    method: 'post',
    data: data,
  })
}

// 修改执法设备列(用于首页配套资源)
export function updateXzzfjZfsb(data) {
  return request({
    url: '/xzzfj/xzzfjZfsb',
    method: 'put',
    data: data,
  })
}

// 删除执法设备列(用于首页配套资源)
export function delXzzfjZfsb(id) {
  return request({
    url: '/xzzfj/xzzfjZfsb/remove/' + id,
    method: 'put',
  })
}

// 部门下拉数据
export function getDepartment(query) {
  return request({
    url: '/xzzfj/dutyPersonnel/getDeptList',
    method: 'get',
    params: query
  })
}

// 获取用户区域
export function getAreaName(id) {
  return request({
    url: '/xzzfj/xzzfjZfsb/getArea',
    method: 'get',
  })
}

//六大类的统计
export function getClassify(id) {
  return request({
    url: '/xzzfj/xzzfjZfsb/countZfsb',
    method: 'get',
  })
}

//导出
export function exportData(params) {
  return request({
    url: '/xzzfj/xzzfjZfsb/export',
    method: 'post',
    responseType:"blob",
    params
  })
}
