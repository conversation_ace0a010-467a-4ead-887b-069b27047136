import request from '@/utils/request'

// 获取指标信息列表
export function findZbDataList(query) {
  return request({
    url: '/screen/zbgl/findZbDataList',
    method: 'get',
    params: query
  })
}
//添加指标信息
export function addZb(query) {
  return request({
    url: '/screen/zbgl/addZb',
    method: 'post',
    data: query
  })
}
//更新指标信息
export function updateZb(query) {
  return request({
    url: '/screen/zbgl/updateZb',
    method: 'post',
    data: query
  })
}
//获取某个指标信息
export function getZbData(query) {
  return request({
    url: '/screen/zbgl/getZbData',
    method: 'get',
    params: query
  })
}
//调试指标接口
export function debugScript(query) {
  return request({
    url: '/screen/zbgl/debugScript',
    method: 'post',
    data: query
  })
}
//删除指标信息
export function removeZb(query) {
  return request({
    url: '/screen/zbgl/removeZb',
    method: 'post',
    params: query
  })
}
//获取指标接口文档
export function zbdcwd(query) {
  return request({
    url: '/screen/zbgldc/zbdcwd',
    method: 'get',
    params: query
  })
}
//获取大屏专题
export function getDpztList(query) {
  return request({
    url: '/screen/menu/getSceMenuList',
    method: 'get',
    params: query
  })
}
//导出指定的指标接口文档
export function exportApiToPdf(query) {
  return request({
    url: '/screen/zbgldc/exportApiToPdf',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
//检验指标编码是否已存在
export function isSameBm(query) {
  return request({
    url: '/screen/zbgl/isSameBm',
    method: 'get',
    params: query
  })
}

export function findZbRecordDataList(query) {
  return request({
    url: '/screen/zbgl/findZbRecordDataList',
    method: 'get',
    params: query
  })
}
export function getZbRecordData(query) {
  return request({
    url: '/screen/zbgl/getZbRecordData',
    method: 'get',
    params: query
  })
}

// 指标类
export function getCsyxsmtz(query) {
  return request({
    url: '/screen/index/getCsyxsmtz',
    method: 'get',
    params: query
  })
}
export function getCsyxsmtzLevel(query) {
  return request({
    url: '/screen/index/getCsyxsmtzLevel',
    method: 'get',
    params: query
  })
}

export function getZbx(query) {
  return request({
    url: '/screen/index/getZbx',
    method: 'get',
    params: query
  })
}

//审核记录
export function auditRecords(id) {
  return request({
    url: '/screen/zbgl/auditRecords?indexid='+id,
    method: 'get',
  })
}

//待审核列表
export function auditList(query) {
  return request({
    url: '/screen/zbgl/auditList',
    method: 'get',
    params: query
  })
}

//审核通过
export function adopt(id) {
  return request({
    url: '/screen/zbgl/adopt?id='+id,
    method: 'get',
  })
}

//审核不通过
export function refuse(query) {
  return request({
    url: '/screen/zbgl/refuse',
    method: 'get',
    params: query
  })
}

// 查询指标接口调用日志列表
export function findZbLogList(query) {
  return request({
    url: '/system/tbYwzbpzbPortcallLog20206/list',
    method: 'get',
    params: query
  })
}

//启用、停用指标信息
export function changeZb(query) {
  return request({
    url: '/screen/zbgl/tyqy',
    method: 'post',
    data: query
  })
}