import request from '@/utils/request'

// 阈值规则列表查询
export function getThresholdRuleList(query) {
  return request({
    url: '/screen/thresholdRule/list',
    method: 'get',
    params: query,
  })
}

// 新增阈值规则
export function addThresholdRule(data) {
  return request({
    url: '/screen/thresholdRule',
    method: 'post',
    data: data,
  })
}

// 编辑阈值规则
export function editThresholdRule(data) {
  return request({
    url: '/screen/thresholdRule',
    method: 'put',
    data: data,
  })
}

// 删除阈值规则
export function delThresholdRule(id) {
  return request({
    url: `/screen/thresholdRule/remove/${id}`,
    method: 'put',
  })
}

// 获取指标阈值配置列表
export function getThresholdConfigureList(query) {
  return request({
    url: '/screen/thresholdConfigure/list',
    method: 'get',
    params: query,
  })
}

// 根据指标id查询配置指标阈值页面信息
export function findZbyzByIndicatorId(id) {
  return request({
    url: `/screen/thresholdConfigure/findByIndicatorId/${id}`,
    method: 'get',
  })
}

// 修改配置指标阈值
export function editIndexExtend(data) {
  return request({
    url: '/screen/extend/update',
    method: 'put',
    data: data,
  })
}

// 新增分段阈值
export function addThresholdConfigure(data) {
  return request({
    url: '/screen/thresholdConfigure',
    method: 'post',
    data: data,
  })
}

// 根据指标id查询分段阈值
export function getThresholdLevel(id) {
  return request({
    url: `/screen/thresholdConfigure/thresholdLevel/${id}`,
    method: 'get',
  })
}

// 根据分段阈值id删除分段阈值
export function delThresholdLevel(id) {
  return request({
    url: `/screen/thresholdConfigure/${id}`,
    method: 'delete',
  })
}

// 根据指标id删除配置阈值
export function delConfigureYzByid(id) {
  return request({
    url: `/screen/thresholdConfigure/configure/${id}`,
    method: 'put',
  })
}

// 根据指标id手动执行接口
export function doHandRunByid(id) {
  return request({
    url: `/screen/thresholdConfigure/run/${id}`,
    method: 'get',
  })
}
