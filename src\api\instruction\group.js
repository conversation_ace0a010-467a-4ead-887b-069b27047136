import request from '@/utils/request'

// 查询群体基本信息列表
export function listGroup(query) {
  return request({
    url: '/instruction/group/list',
    method: 'get',
    params: query,
  })
}

// 查询群体基本信息详细
export function getGroup(id) {
  return request({
    url: '/instruction/group/' + id,
    method: 'get',
  })
}

// 新增群体基本信息
export function addGroup(data) {
  return request({
    url: '/instruction/group',
    method: 'post',
    data: data,
  })
}

// 修改群体基本信息
export function updateGroup(data) {
  return request({
    url: '/instruction/group',
    method: 'put',
    data: data,
  })
}

// 删除群体基本信息
export function delGroup(id) {
  return request({
    url: '/instruction/group/' + id,
    method: 'delete',
  })
}

// 导入群体模板下载接口
export function importGroupTemplate(query) {
  return request({
    url: 'instruction/group/importTemplate',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 导出群体数据接口
export function exportGroups(query) {
  return request({
    url: 'instruction/group/export',
    method: 'post',
    params: query,
    responseType: 'blob',
  })
}

// 导入群体数据接口
export function uploadGroup(query) {
  return request({
    url: 'instruction/group/importData',
    method: 'post',
  })
}
