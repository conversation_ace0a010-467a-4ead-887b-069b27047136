import request from '@/utils/request'

/**
 * @description: 查询任务信息列表
 * * @return {*}
 */
export function getList(params, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo/list',
    method: 'get',
    params,
    ...other,
  })
}

/**
 * @description: 导出任务信息列表
 * * @return {*}
 */
export function exportTaskInfo(data, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo/export',
    method: 'post',
    responseType: 'blob',
    data,
    ...other,
  })
}

/**
 * @description: 获取任务信息详细信息
 * * @return {*}
 */
export function getTaskInfo(id, params, other = {}) {
  return request({
    url: `/zhcg/task/taskInfo/${id}`,
    method: 'get',
    params,
    ...other,
  })
}

/**
 * @description: 新增任务信息
 * * @return {*}
 */
export function addTaskInfo(data, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo',
    method: 'post',
    data,
    ...other,
  })
}

/**
 * @description: 修改任务信息
 * * @return {*}
 */
export function updateTaskInfo(data, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo',
    method: 'put',
    data,
    ...other,
  })
}

/**
 * @description: 删除任务信息
 * * @return {*}
 */
export function deleteTaskInfo(ids, data, other = {}) {
  return request({
    url: `/zhcg/task/taskInfo/${ids}`,
    method: 'put',
    data,
    ...other,
  })
}

/**
 * @description: 签收
 * * @return {*}
 */
export function signTaskInfo(id) {
  return request({
    url: `/zhcg/task/taskInfo/sign/${id}`,
    method: 'post',
  })
}

/**
 * @description: 处置（提交）
 * * @return {*}
 */
export function handleTaskInfo(data, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo/handle',
    method: 'post',
    data,
    ...other,
  })
}

/**
 * @description: 挂起
 * * @return {*}
 */
export function hangUpTaskInfo(data, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo/hangUp',
    method: 'post',
    data,
    ...other,
  })
}

/**
 * @description: 重启
 * * @return {*}
 */
export function restartTaskInfo(data, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo/restart',
    method: 'post',
    data,
    ...other,
  })
}

/**
 * @description: 退回
 * * @return {*}
 */
export function returnBackTaskInfo(id, data, other = {}) {
  return request({
    url: `/zhcg/task/taskInfo/returnBack/${id}`,
    method: 'post',
    data,
    ...other,
  })
}

/**
 * @description: 转派
 * * @return {*}
 */
export function transferTaskInfo(data, other = {}) {
  return request({
    url: '/zhcg/task/taskInfo/transfer',
    method: 'post',
    data,
    ...other,
  })
}

/**
 * @description: 催办任务
 * @param {number} id - 任务ID
 * @return {*}
 */
export function remindTaskInfo(id, data, other = {}) {
  return request({
    url: `/zhcg/task/taskInfo/remind/${id}`,
    method: 'post',
    data,
    ...other,
  })
}
