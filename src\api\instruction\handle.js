import request from '@/utils/request'

// 查询督办单列表
export function listHandle(query) {
  return request({
    url: '/instruction/handle/list',
    method: 'get',
    params: query,
  })
}

// 查询督办单详细
export function getHandle(id) {
  return request({
    url: '/instruction/handle/' + id,
    method: 'get',
  })
}

// 新增督办单
export function addHandle(data) {
  return request({
    url: '/instruction/handle',
    method: 'post',
    data: data,
  })
}

// 修改督办单
export function updateHandle(data) {
  return request({
    url: '/instruction/handle',
    method: 'put',
    data: data,
  })
}

// 删除督办单
export function delHandle(id) {
  return request({
    url: '/instruction/handle/' + id,
    method: 'delete',
  })
}
// 指令列表获取督办单/提醒单信息 Copy
export function getBaseInfo(data) {
  return request({
    url: '/instruction/handle/getBaseInfo',
    method: 'post',
    data,
  })
}

// 导出督办单列表
export function exportHandle() {
  return request({
    url: '/instruction/handle/export',
    responseType: 'blob',
    method: 'post',
  })
}
