import request from '@/utils/request'

// 查询监测点位
export function getMonitorPoints(query) {
  return request({
    url: '/MonitorPoint/MonitorPoint/listAll',
    method: 'get',
    params: query,
  })
}

// 获取某个监测点位
export function getPoint(id) {
  return request({
    url: `/MonitorPoint/MonitorPoint/${id}`,
    method: 'get',
  })
}

// 删除监测点位
export function deletePoint(id) {
  return request({
    url: `/MonitorPoint/MonitorPoint/${id}`,
    method: 'put',
  })
}

// 新增监测点位
export function addPoint(data) {
  return request({
    url: '/MonitorPoint/MonitorPoint',
    method: 'post',
    data
  })
}

// 编辑监测点位
export function updatePoints(data) {
  return request({
    url: '/MonitorPoint/MonitorPoint',
    method: 'put',
    data
  })
}
